/**
 * Real-time Notification Service using Supabase
 * Handles instant notifications, online status, and presence indicators
 */

import { getSupabaseClient, getSupabaseServiceClient, NotificationRealtime, UserPresence } from '../supabase/client';
import { RealtimeChannel } from '@supabase/supabase-js';

export interface NotificationEvent {
  type: 'new_notification' | 'notification_read' | 'user_online' | 'user_offline';
  data: any;
  userId?: string;
}

export interface OnlineUser {
  userId: string;
  status: 'online' | 'offline' | 'away';
  lastSeen: string;
}

class RealtimeNotificationService {
  private supabase: any = null;
  private serviceSupabase: any = null;
  private channels: Map<string, RealtimeChannel> = new Map();
  private notificationListeners: Map<string, (event: NotificationEvent) => void> = new Map();
  private presenceListeners: Map<string, (users: OnlineUser[]) => void> = new Map();
  private currentUserId: string | null = null;
  private heartbeatInterval: NodeJS.Timeout | null = null;

  /**
   * Initialize Supabase clients lazily
   */
  private initializeSupabase() {
    if (!this.supabase) {
      try {
        this.supabase = getSupabaseClient();
        this.serviceSupabase = getSupabaseServiceClient();
      } catch (error) {
        console.warn('Supabase not configured, real-time features will be disabled:', error);
        return false;
      }
    }
    return true;
  }

  /**
   * Initialize the notification service
   */
  initialize(userId: string) {
    if (!this.initializeSupabase()) {
      console.warn('Skipping real-time notification initialization due to missing Supabase configuration');
      return;
    }

    this.currentUserId = userId;
    this.updatePresence('online');
    this.startHeartbeat();
  }

  /**
   * Subscribe to notifications for current user
   */
  subscribeToNotifications(onNotification: (event: NotificationEvent) => void) {
    if (!this.initializeSupabase()) {
      console.warn('Skipping notification subscription due to missing Supabase configuration');
      return;
    }

    if (!this.currentUserId) {
      console.error('User not initialized');
      return;
    }

    const channelName = `notifications:${this.currentUserId}`;
    
    // Remove existing subscription if any
    this.unsubscribeFromNotifications();

    const channel = this.supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'notifications_realtime',
          filter: `recipient_id=eq.${this.currentUserId}`,
        },
        (payload) => {
          const notification = payload.new as NotificationRealtime;
          onNotification({
            type: 'new_notification',
            data: notification,
            userId: this.currentUserId!,
          });
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'notifications_realtime',
          filter: `recipient_id=eq.${this.currentUserId}`,
        },
        (payload) => {
          const notification = payload.new as NotificationRealtime;
          if (notification.read_at) {
            onNotification({
              type: 'notification_read',
              data: notification,
              userId: this.currentUserId!,
            });
          }
        }
      )
      .subscribe();

    this.channels.set('notifications', channel);
    this.notificationListeners.set('notifications', onNotification);
  }

  /**
   * Subscribe to user presence updates
   */
  subscribeToPresence(userIds: string[], onPresenceUpdate: (users: OnlineUser[]) => void) {
    const channelName = 'presence:global';
    
    // Remove existing subscription if any
    this.unsubscribeFromPresence();

    const channel = this.supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'user_presence',
        },
        async () => {
          // Fetch current presence for specified users
          const onlineUsers = await this.getOnlineUsers(userIds);
          onPresenceUpdate(onlineUsers);
        }
      )
      .subscribe();

    this.channels.set('presence', channel);
    this.presenceListeners.set('presence', onPresenceUpdate);

    // Initial fetch
    this.getOnlineUsers(userIds).then(onPresenceUpdate);
  }

  /**
   * Send a real-time notification
   */
  async sendRealtimeNotification(
    mysqlNotificationId: string,
    recipientId: string,
    type: string,
    data: any
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await this.serviceSupabase
        .from('notifications_realtime')
        .insert({
          mysql_id: mysqlNotificationId,
          recipient_id: recipientId,
          type,
          data,
        });

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Mark notification as read
   */
  async markNotificationAsRead(mysqlNotificationId: string): Promise<void> {
    try {
      await this.serviceSupabase
        .from('notifications_realtime')
        .update({ read_status: true })
        .eq('mysql_id', mysqlNotificationId);
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  }

  /**
   * Update user presence status
   */
  async updatePresence(status: 'online' | 'offline' | 'away'): Promise<void> {
    if (!this.initializeSupabase() || !this.currentUserId) return;

    try {
      await this.serviceSupabase.rpc('update_user_presence', {
        p_user_id: this.currentUserId,
        p_status: status,
      });
    } catch (error) {
      console.error('Error updating presence:', error);
    }
  }

  /**
   * Get online users from a list of user IDs
   */
  async getOnlineUsers(userIds: string[]): Promise<OnlineUser[]> {
    if (userIds.length === 0) return [];

    try {
      const { data, error } = await this.supabase
        .from('user_presence')
        .select('user_id, status, last_seen')
        .in('user_id', userIds);

      if (error || !data) return [];

      return data.map((user) => ({
        userId: user.user_id,
        status: user.status as 'online' | 'offline' | 'away',
        lastSeen: user.last_seen,
      }));
    } catch (error) {
      console.error('Error fetching online users:', error);
      return [];
    }
  }

  /**
   * Check if a specific user is online
   */
  async isUserOnline(userId: string): Promise<boolean> {
    try {
      const { data, error } = await this.supabase
        .from('user_presence')
        .select('status, last_seen')
        .eq('user_id', userId)
        .single();

      if (error || !data) return false;

      // Consider user online if status is 'online' and last seen is within 2 minutes
      const isRecentlyActive = new Date(data.last_seen).getTime() > Date.now() - 2 * 60 * 1000;
      return data.status === 'online' && isRecentlyActive;
    } catch (error) {
      console.error('Error checking user online status:', error);
      return false;
    }
  }

  /**
   * Start heartbeat to maintain presence
   */
  private startHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }

    this.heartbeatInterval = setInterval(() => {
      if (this.currentUserId) {
        this.updatePresence('online');
      }
    }, 30000); // Update every 30 seconds
  }

  /**
   * Stop heartbeat
   */
  private stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  /**
   * Unsubscribe from notifications
   */
  unsubscribeFromNotifications() {
    const channel = this.channels.get('notifications');
    if (channel) {
      this.supabase.removeChannel(channel);
      this.channels.delete('notifications');
    }
    this.notificationListeners.delete('notifications');
  }

  /**
   * Unsubscribe from presence updates
   */
  unsubscribeFromPresence() {
    const channel = this.channels.get('presence');
    if (channel) {
      this.supabase.removeChannel(channel);
      this.channels.delete('presence');
    }
    this.presenceListeners.delete('presence');
  }

  /**
   * Cleanup all subscriptions and set user offline
   */
  cleanup() {
    this.stopHeartbeat();
    
    this.channels.forEach((channel) => {
      this.supabase.removeChannel(channel);
    });
    this.channels.clear();
    this.notificationListeners.clear();
    this.presenceListeners.clear();

    if (this.currentUserId) {
      this.updatePresence('offline');
    }
  }

  /**
   * Handle page visibility changes
   */
  handleVisibilityChange() {
    if (!this.currentUserId) return;

    if (document.hidden) {
      this.updatePresence('away');
    } else {
      this.updatePresence('online');
    }
  }
}

// Export singleton instance
export const realtimeNotificationService = new RealtimeNotificationService();
